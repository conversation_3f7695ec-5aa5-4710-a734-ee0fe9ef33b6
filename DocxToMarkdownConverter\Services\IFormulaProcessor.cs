using DocxToMarkdownConverter.Models;
using DocumentFormat.OpenXml.Math;
using W = DocumentFormat.OpenXml.Wordprocessing;

namespace DocxToMarkdownConverter.Services;

public interface IFormulaProcessor
{
    /// <summary>
    /// Processes a mathematical formula and converts it to appropriate markdown format
    /// </summary>
    /// <param name="formula">The formula element (OfficeMath)</param>
    /// <param name="options">Conversion options</param>
    /// <returns>Formatted formula string</returns>
    string ProcessFormula(object formula, ConversionOptions options);
    
    /// <summary>
    /// Converts a formula to LaTeX format
    /// </summary>
    /// <param name="formula">The formula element</param>
    /// <returns>LaTeX representation of the formula</returns>
    Task<string> ConvertToLatexAsync(object formula);
    
    /// <summary>
    /// Converts a formula to MathML format
    /// </summary>
    /// <param name="formula">The formula element</param>
    /// <returns>MathML representation of the formula</returns>
    Task<string> ConvertToMathMLAsync(object formula);
    
    /// <summary>
    /// Detects if an element contains mathematical formulas
    /// </summary>
    /// <param name="element">The element to check</param>
    /// <returns>True if the element contains formulas</returns>
    bool ContainsFormulas(object element);
    
    /// <summary>
    /// Extracts all formulas from a paragraph
    /// </summary>
    /// <param name="paragraph">The paragraph to process</param>
    /// <returns>Collection of formula elements</returns>
    IEnumerable<object> ExtractFormulas(W.Paragraph paragraph);
}