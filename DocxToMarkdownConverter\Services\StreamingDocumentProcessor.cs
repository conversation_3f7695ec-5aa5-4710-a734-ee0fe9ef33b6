using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.Extensions.Logging;
using DocxToMarkdownConverter.Models;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 流式文档处理器实现
/// </summary>
public class StreamingDocumentProcessor : IStreamingDocumentProcessor, IDisposable
{
    private readonly ILogger<StreamingDocumentProcessor> _logger;
    private readonly ITextProcessor _textProcessor;
    private readonly IImageProcessor _imageProcessor;
    private readonly ITableProcessor _tableProcessor;

    private long _memoryLimit = 100 * 1024 * 1024; // 100MB 默认限制
    private long _currentMemoryUsage;
    private long _maxMemoryUsage;
    private int _activeBuffers;
    private readonly object _memoryLock = new();

    // 缓冲区大小常量
    private const int SmallFileBufferSize = 8 * 1024; // 8KB
    private const int MediumFileBufferSize = 64 * 1024; // 64KB
    private const int LargeFileBufferSize = 256 * 1024; // 256KB

    // 文件大小阈值
    private const long SmallFileThreshold = 1 * 1024 * 1024; // 1MB
    private const long MediumFileThreshold = 10 * 1024 * 1024; // 10MB
    private const long LargeFileThreshold = 100 * 1024 * 1024; // 100MB

    public StreamingDocumentProcessor(
        ILogger<StreamingDocumentProcessor> logger,
        ITextProcessor textProcessor,
        IImageProcessor imageProcessor,
        ITableProcessor tableProcessor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _textProcessor = textProcessor ?? throw new ArgumentNullException(nameof(textProcessor));
        _imageProcessor = imageProcessor ?? throw new ArgumentNullException(nameof(imageProcessor));
        _tableProcessor = tableProcessor ?? throw new ArgumentNullException(nameof(tableProcessor));
    }

    public async Task<ConversionResult> ProcessDocumentStreamAsync(
        Stream inputStream,
        Stream outputStream,
        ConversionOptions options,
        IProgress<ConversionProgress>? progress = null,
        CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.Now;
        var result = new ConversionResult();

        try
        {
            _logger.LogInformation("开始流式处理文档，大小: {Size} 字节", inputStream.Length);

            var strategy = DetermineProcessingStrategy(inputStream.Length);
            _logger.LogDebug("选择处理策略: {Strategy}", strategy);

            result.InputSize = inputStream.Length;

            // 根据策略选择处理方法
            switch (strategy)
            {
                case DocumentProcessingStrategy.InMemory:
                    await ProcessInMemoryAsync(inputStream, outputStream, options, progress, cancellationToken);
                    break;
                case DocumentProcessingStrategy.Streaming:
                    await ProcessStreamingAsync(inputStream, outputStream, options, progress, cancellationToken);
                    break;
                case DocumentProcessingStrategy.Chunked:
                    await ProcessChunkedAsync(inputStream, outputStream, options, progress, cancellationToken);
                    break;
                case DocumentProcessingStrategy.TemporaryFile:
                    await ProcessWithTemporaryFileAsync(inputStream, outputStream, options, progress, cancellationToken);
                    break;
            }

            result.OutputSize = outputStream.Length;
            result.Duration = DateTime.Now - startTime;
            result.IsSuccess = true;

            _logger.LogInformation("流式处理完成，耗时: {Duration:F2}秒", result.Duration.TotalSeconds);
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            result.Duration = DateTime.Now - startTime;

            _logger.LogError(ex, "流式处理文档时发生错误");
        }

        return result;
    }

    public DocumentProcessingStrategy DetermineProcessingStrategy(long documentSizeBytes)
    {
        return documentSizeBytes switch
        {
            <= SmallFileThreshold => DocumentProcessingStrategy.InMemory,
            <= MediumFileThreshold => DocumentProcessingStrategy.Streaming,
            <= LargeFileThreshold => DocumentProcessingStrategy.Chunked,
            _ => DocumentProcessingStrategy.TemporaryFile
        };
    }

    public int GetRecommendedBufferSize(long documentSizeBytes)
    {
        return documentSizeBytes switch
        {
            <= SmallFileThreshold => SmallFileBufferSize,
            <= MediumFileThreshold => MediumFileBufferSize,
            _ => LargeFileBufferSize
        };
    }

    public async IAsyncEnumerable<DocumentElement> ReadDocumentElementsAsync(
        Stream inputStream,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        using var document = WordprocessingDocument.Open(inputStream, false);
        var body = document.MainDocumentPart?.Document?.Body;

        if (body == null)
        {
            _logger.LogWarning("文档主体为空");
            yield break;
        }

        long position = 0;
        foreach (var element in body.Elements())
        {
            cancellationToken.ThrowIfCancellationRequested();

            var documentElement = await ConvertToDocumentElementAsync(element, position);
            if (documentElement != null)
            {
                yield return documentElement;
                position += documentElement.Size;
            }

            // 检查内存使用
            if (ShouldYieldForMemoryPressure())
            {
                await Task.Delay(10, cancellationToken); // 短暂暂停以允许垃圾回收
            }
        }
    }

    public async Task WriteMarkdownStreamAsync(
        IAsyncEnumerable<MarkdownElement> markdownElements,
        Stream outputStream,
        CancellationToken cancellationToken = default)
    {
        using var writer = new StreamWriter(outputStream, Encoding.UTF8, bufferSize: GetRecommendedBufferSize(outputStream.Length), leaveOpen: true);

        await foreach (var element in markdownElements.WithCancellation(cancellationToken))
        {
            await writer.WriteLineAsync(element.Content);
            await writer.WriteLineAsync(); // 添加空行分隔

            // 定期刷新缓冲区
            if (writer.BaseStream.Position % (64 * 1024) == 0)
            {
                await writer.FlushAsync();
            }

            UpdateMemoryUsage(element.EstimatedSize);
        }

        await writer.FlushAsync();
    }

    public long EstimateMemoryUsage(long documentSizeBytes, ConversionOptions options)
    {
        // 基础内存使用估算
        var baseMemory = documentSizeBytes * 2; // 文档内容 + 解析结构

        // 根据选项调整估算
        if (options.ExtractImages)
            baseMemory += (long)(documentSizeBytes * 0.5); // 图片处理额外内存

        if (options.ConvertTables)
            baseMemory += (long)(documentSizeBytes * 0.3); // 表格处理额外内存

        if (options.ProcessFormulas)
            baseMemory += (long)(documentSizeBytes * 0.2); // 公式处理额外内存

        return (long)baseMemory;
    }

    public void SetMemoryLimit(long maxMemoryBytes)
    {
        lock (_memoryLock)
        {
            _memoryLimit = maxMemoryBytes;
            _logger.LogInformation("内存限制已设置为: {Limit:N0} 字节", maxMemoryBytes);
        }
    }

    public StreamingProcessorMemoryInfo GetMemoryInfo()
    {
        lock (_memoryLock)
        {
            return new StreamingProcessorMemoryInfo
            {
                CurrentMemoryUsage = _currentMemoryUsage,
                MaxMemoryUsage = _maxMemoryUsage,
                MemoryLimit = _memoryLimit,
                MemoryUsagePercentage = _memoryLimit > 0 ? (double)_currentMemoryUsage / _memoryLimit * 100 : 0,
                ActiveBuffers = _activeBuffers,
                BufferMemoryUsage = _activeBuffers * LargeFileBufferSize,
                LastUpdated = DateTime.Now
            };
        }
    }

    private async Task ProcessInMemoryAsync(
        Stream inputStream,
        Stream outputStream,
        ConversionOptions options,
        IProgress<ConversionProgress>? progress,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("使用内存处理模式");

        // 将整个文档加载到内存中处理
        var buffer = new byte[inputStream.Length];
        await inputStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

        using var memoryStream = new MemoryStream(buffer);
        using var document = WordprocessingDocument.Open(memoryStream, false);

        var markdownContent = await ProcessDocumentInMemoryAsync(document, options, progress, cancellationToken);
        var markdownBytes = Encoding.UTF8.GetBytes(markdownContent);
        await outputStream.WriteAsync(markdownBytes, 0, markdownBytes.Length, cancellationToken);
    }

    private async Task ProcessStreamingAsync(
        Stream inputStream,
        Stream outputStream,
        ConversionOptions options,
        IProgress<ConversionProgress>? progress,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("使用流式处理模式");

        var elementCount = 0;
        var processedCount = 0;

        // 首先计算总元素数量用于进度报告
        await foreach (var _ in ReadDocumentElementsAsync(inputStream, cancellationToken))
        {
            elementCount++;
        }

        // 重置流位置
        inputStream.Position = 0;

        // 流式处理元素
        await foreach (var element in ReadDocumentElementsAsync(inputStream, cancellationToken))
        {
            var markdownElement = await ConvertToMarkdownElementAsync(element, options);
            if (markdownElement != null)
            {
                await WriteMarkdownElementAsync(outputStream, markdownElement, cancellationToken);
            }

            processedCount++;
            progress?.Report(new ConversionProgress
            {
                ProgressPercentage = (double)processedCount / elementCount * 100,
                CurrentOperation = $"处理元素 {processedCount}/{elementCount}",
                ProcessedItems = processedCount,
                TotalItems = elementCount
            });
        }
    }

    private async Task ProcessChunkedAsync(
        Stream inputStream,
        Stream outputStream,
        ConversionOptions options,
        IProgress<ConversionProgress>? progress,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("使用分块处理模式");

        const int chunkSize = 1024 * 1024; // 1MB 块
        var buffer = new byte[chunkSize];
        var totalBytesRead = 0L;
        var totalBytes = inputStream.Length;

        using var tempStream = new MemoryStream();

        while (totalBytesRead < totalBytes)
        {
            var bytesToRead = (int)Math.Min(chunkSize, totalBytes - totalBytesRead);
            var bytesRead = await inputStream.ReadAsync(buffer, 0, bytesToRead, cancellationToken);

            if (bytesRead == 0) break;

            await tempStream.WriteAsync(buffer, 0, bytesRead, cancellationToken);
            totalBytesRead += bytesRead;

            progress?.Report(new ConversionProgress
            {
                ProgressPercentage = (double)totalBytesRead / totalBytes * 100,
                CurrentOperation = $"读取数据块 {totalBytesRead:N0}/{totalBytes:N0} 字节"
            });

            // 检查内存压力
            if (ShouldYieldForMemoryPressure())
            {
                await ForceGarbageCollectionAsync();
            }
        }

        // 处理完整的临时流
        tempStream.Position = 0;
        await ProcessStreamingAsync(tempStream, outputStream, options, progress, cancellationToken);
    }

    private async Task ProcessWithTemporaryFileAsync(
        Stream inputStream,
        Stream outputStream,
        ConversionOptions options,
        IProgress<ConversionProgress>? progress,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("使用临时文件处理模式");

        var tempFilePath = Path.GetTempFileName();
        try
        {
            // 将输入流写入临时文件
            using (var tempFileStream = File.Create(tempFilePath))
            {
                await inputStream.CopyToAsync(tempFileStream, GetRecommendedBufferSize(inputStream.Length), cancellationToken);
            }

            // 从临时文件流式处理
            using var tempReadStream = File.OpenRead(tempFilePath);
            await ProcessStreamingAsync(tempReadStream, outputStream, options, progress, cancellationToken);
        }
        finally
        {
            // 清理临时文件
            if (File.Exists(tempFilePath))
            {
                try
                {
                    File.Delete(tempFilePath);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "删除临时文件失败: {TempFile}", tempFilePath);
                }
            }
        }
    }

    private async Task<DocumentElement?> ConvertToDocumentElementAsync(OpenXmlElement element, long position)
    {
        return element switch
        {
            Paragraph paragraph => await ConvertParagraphAsync(paragraph, position),
            Table table => await ConvertTableAsync(table, position),
            _ => null
        };
    }

    private async Task<ParagraphElement> ConvertParagraphAsync(Paragraph paragraph, long position)
    {
        var paragraphElement = new ParagraphElement
        {
            ElementType = "Paragraph",
            Position = position,
            Size = EstimateElementSize(paragraph)
        };

        var textBuilder = new StringBuilder();

        // Check if this paragraph contains math formulas
        bool containsMath = paragraph.Descendants<DocumentFormat.OpenXml.Math.OfficeMath>().Any();

        if (containsMath)
        {
            // For paragraphs with math, skip text extraction to avoid corrupted math content
            // The math will be processed separately by the formula processor
            _logger.LogDebug("Paragraph contains math formulas, skipping text extraction in streaming processor");
            paragraphElement.Text = "<!-- Paragraph contains math formulas -->";
        }
        else
        {
            // For non-math paragraphs, process runs normally
            foreach (var run in paragraph.Elements<Run>())
            {
                // Use TextProcessor logic instead of direct InnerText
                var runText = ExtractRunTextSafely(run);
                textBuilder.Append(runText);

                var textRun = new TextRun
                {
                    Text = runText,
                    IsBold = run.RunProperties?.Bold != null,
                    IsItalic = run.RunProperties?.Italic != null,
                    IsUnderline = run.RunProperties?.Underline != null
                };

                paragraphElement.TextRuns.Add(textRun);
            }

            paragraphElement.Text = textBuilder.ToString();
        }

        // 处理段落样式
        if (paragraph.ParagraphProperties != null)
        {
            paragraphElement.Style = new ParagraphStyle
            {
                StyleName = paragraph.ParagraphProperties.ParagraphStyleId?.Val?.Value ?? "",
                OutlineLevel = paragraph.ParagraphProperties.OutlineLevel?.Val ?? 0
            };
        }

        UpdateMemoryUsage(paragraphElement.Size);
        await Task.CompletedTask; // 消除async警告
        return paragraphElement;
    }

    private string ExtractRunTextSafely(Run run)
    {
        var textBuilder = new StringBuilder();

        try
        {
            // Process text elements in the run (similar to TextProcessor logic)
            foreach (var element in run.Elements())
            {
                if (element is DocumentFormat.OpenXml.Wordprocessing.Text textElement)
                {
                    textBuilder.Append(textElement.Text);
                }
                else if (element is DocumentFormat.OpenXml.Wordprocessing.Break)
                {
                    textBuilder.AppendLine();
                }
                else if (element is DocumentFormat.OpenXml.Wordprocessing.TabChar)
                {
                    textBuilder.Append("\t");
                }
                // Skip other elements that might contain math content
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting text from run safely");
            // Fallback: return empty string instead of corrupted text
            return string.Empty;
        }

        return textBuilder.ToString();
    }

    private async Task<TableElement> ConvertTableAsync(Table table, long position)
    {
        var tableElement = new TableElement
        {
            ElementType = "Table",
            Position = position,
            Size = EstimateElementSize(table)
        };

        foreach (var row in table.Elements<DocumentFormat.OpenXml.Wordprocessing.TableRow>())
        {
            var tableRow = new TableRow();

            foreach (var cell in row.Elements<DocumentFormat.OpenXml.Wordprocessing.TableCell>())
            {
                var tableCell = new TableCell
                {
                    Text = cell.InnerText
                };
                tableRow.Cells.Add(tableCell);
            }

            tableElement.Rows.Add(tableRow);
        }

        UpdateMemoryUsage(tableElement.Size);
        await Task.CompletedTask; // 消除async警告
        return tableElement;
    }

    private async Task<MarkdownElement?> ConvertToMarkdownElementAsync(DocumentElement element, ConversionOptions options)
    {
        return element switch
        {
            ParagraphElement paragraph => await ConvertParagraphToMarkdownAsync(paragraph, options),
            TableElement table => await ConvertTableToMarkdownAsync(table, options),
            ImageElement image => await ConvertImageToMarkdownAsync(image, options),
            _ => null
        };
    }

    private async Task<MarkdownParagraph> ConvertParagraphToMarkdownAsync(ParagraphElement paragraph, ConversionOptions options)
    {
        var markdown = new MarkdownParagraph
        {
            Content = _textProcessor.ProcessParagraph(paragraph, options),
            EstimatedSize = paragraph.Text.Length * 2 // 估算Markdown大小
        };

        await Task.CompletedTask; // 消除async警告
        return markdown;
    }

    private async Task<MarkdownTable> ConvertTableToMarkdownAsync(TableElement table, ConversionOptions options)
    {
        var markdownTable = new MarkdownTable();

        if (table.Rows.Count > 0)
        {
            // 第一行作为标题
            var headerRow = table.Rows[0];
            markdownTable.Headers = headerRow.Cells.Select(c => c.Text).ToList();

            // 其余行作为数据
            for (int i = 1; i < table.Rows.Count; i++)
            {
                var dataRow = table.Rows[i].Cells.Select(c => c.Text).ToList();
                markdownTable.Rows.Add(dataRow);
            }
        }

        markdownTable.Content = _tableProcessor.ProcessTable(table, options);
        markdownTable.EstimatedSize = markdownTable.Content.Length;

        await Task.CompletedTask; // 消除async警告
        return markdownTable;
    }

    private async Task<MarkdownImage> ConvertImageToMarkdownAsync(ImageElement image, ConversionOptions options)
    {
        var markdownImage = new MarkdownImage
        {
            AltText = $"Image {image.ImageId}",
            ImagePath = await _imageProcessor.ProcessImageAsync(image.ImageData ?? new object(), options.OutputDirectory)
        };

        markdownImage.Content = $"![{markdownImage.AltText}]({markdownImage.ImagePath})";
        markdownImage.EstimatedSize = markdownImage.Content.Length;

        return markdownImage;
    }

    private async Task WriteMarkdownElementAsync(Stream outputStream, MarkdownElement element, CancellationToken cancellationToken)
    {
        var bytes = Encoding.UTF8.GetBytes(element.Content + Environment.NewLine + Environment.NewLine);
        await outputStream.WriteAsync(bytes, 0, bytes.Length, cancellationToken);
    }

    private async Task<string> ProcessDocumentInMemoryAsync(
        WordprocessingDocument document,
        ConversionOptions options,
        IProgress<ConversionProgress>? progress,
        CancellationToken cancellationToken)
    {
        var markdownBuilder = new StringBuilder();
        var body = document.MainDocumentPart?.Document?.Body;

        if (body == null) return string.Empty;

        var elements = body.Elements().ToList();
        for (int i = 0; i < elements.Count; i++)
        {
            var element = elements[i];
            var documentElement = await ConvertToDocumentElementAsync(element, i);

            if (documentElement != null)
            {
                var markdownElement = await ConvertToMarkdownElementAsync(documentElement, options);
                if (markdownElement != null)
                {
                    markdownBuilder.AppendLine(markdownElement.Content);
                    markdownBuilder.AppendLine();
                }
            }

            progress?.Report(new ConversionProgress
            {
                ProgressPercentage = (double)(i + 1) / elements.Count * 100,
                CurrentOperation = $"处理元素 {i + 1}/{elements.Count}"
            });
        }

        return markdownBuilder.ToString();
    }

    private int EstimateElementSize(OpenXmlElement element)
    {
        return element.InnerText.Length + element.OuterXml.Length / 10; // 简化的大小估算
    }

    private void UpdateMemoryUsage(int additionalBytes)
    {
        lock (_memoryLock)
        {
            _currentMemoryUsage += additionalBytes;
            if (_currentMemoryUsage > _maxMemoryUsage)
            {
                _maxMemoryUsage = _currentMemoryUsage;
            }
        }
    }

    private bool ShouldYieldForMemoryPressure()
    {
        lock (_memoryLock)
        {
            return _currentMemoryUsage > _memoryLimit * 0.8; // 80% 阈值
        }
    }

    private async Task ForceGarbageCollectionAsync()
    {
        await Task.Run(() =>
        {
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        });

        lock (_memoryLock)
        {
            _currentMemoryUsage = GC.GetTotalMemory(false);
        }

        _logger.LogDebug("强制垃圾回收完成，当前内存使用: {Memory:N0} 字节", _currentMemoryUsage);
    }

    public void Dispose()
    {
        // 清理资源
        lock (_memoryLock)
        {
            _currentMemoryUsage = 0;
            _activeBuffers = 0;
        }
    }
}
