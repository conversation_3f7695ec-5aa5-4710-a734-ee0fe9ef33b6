using System;
using System.Reflection;
using DocxToMarkdownConverter.Services;

namespace DocxToMarkdownConverter
{
    public class TestFormula
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== 数学公式符号间距修复测试 ===\n");
            
            var processor = new FormulaProcessor();
            
            // 使用反射访问私有方法
            var method = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            if (method == null)
            {
                Console.WriteLine("错误：无法找到 PostProcessLatexFormatting 方法");
                return;
            }
            
            // 测试用例1：\timesmin 问题
            Console.WriteLine("测试1：修复 \\timesmin 间距问题");
            var input1 = @"0.9 - 0.2\timesmin(T_consec(t,d(s)),3)";
            var output1 = (string)method.Invoke(processor, new object[] { input1, false });
            Console.WriteLine($"输入：{input1}");
            Console.WriteLine($"输出：{output1}");
            Console.WriteLine($"期望：包含 '\\times min'");
            Console.WriteLine($"结果：{(output1.Contains(@"\times min") ? "✓ 通过" : "✗ 失败")}");
            Console.WriteLine();
            
            // 测试用例2：times 缺少反斜杠
            Console.WriteLine("测试2：修复 times 缺少反斜杠问题");
            var input2 = @"a timesmin(x,y)";
            var output2 = (string)method.Invoke(processor, new object[] { input2, false });
            Console.WriteLine($"输入：{input2}");
            Console.WriteLine($"输出：{output2}");
            Console.WriteLine($"期望：包含 '\\times min'");
            Console.WriteLine($"结果：{(output2.Contains(@"\times min") ? "✓ 通过" : "✗ 失败")}");
            Console.WriteLine();
            
            // 测试用例3：比较运算符间距
            Console.WriteLine("测试3：修复比较运算符间距");
            var input3 = @"T_consec(t,d(s))>0";
            var output3 = (string)method.Invoke(processor, new object[] { input3, false });
            Console.WriteLine($"输入：{input3}");
            Console.WriteLine($"输出：{output3}");
            Console.WriteLine($"期望：运算符周围有适当间距");
            Console.WriteLine($"结果：{(output3.Contains(" > ") ? "✓ 通过" : "✗ 失败")}");
            Console.WriteLine();
            
            // 测试用例4：≤ 符号转换
            Console.WriteLine("测试4：修复 ≤ 符号转换和间距");
            var input4 = @"x≤3";
            var output4 = (string)method.Invoke(processor, new object[] { input4, false });
            Console.WriteLine($"输入：{input4}");
            Console.WriteLine($"输出：{output4}");
            Console.WriteLine($"期望：包含 ' \\leq '");
            Console.WriteLine($"结果：{(output4.Contains(@" \leq ") ? "✓ 通过" : "✗ 失败")}");
            Console.WriteLine();
            
            // 测试用例5：完整公式
            Console.WriteLine("测试5：完整公式测试");
            var input5 = @"0.9 - 0.2\timesmin(T_consec(t,d(s)),3), if T_consec(t,d(s))>0";
            var output5 = (string)method.Invoke(processor, new object[] { input5, true });
            Console.WriteLine($"输入：{input5}");
            Console.WriteLine($"输出：{output5}");
            
            bool hasCorrectTimes = output5.Contains(@"\times min");
            bool hasCorrectSpacing = output5.Contains(" > ");
            Console.WriteLine($"\\times min 修复：{(hasCorrectTimes ? "✓" : "✗")}");
            Console.WriteLine($"运算符间距：{(hasCorrectSpacing ? "✓" : "✗")}");
            Console.WriteLine($"整体测试：{(hasCorrectTimes && hasCorrectSpacing ? "✓ 通过" : "✗ 失败")}");
            
            Console.WriteLine("\n=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
