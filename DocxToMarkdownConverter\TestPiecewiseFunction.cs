using System;
using System.Reflection;
using DocxToMarkdownConverter.Services;

namespace DocxToMarkdownConverter
{
    public class TestPiecewiseFunction
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== 分段函数行分隔符测试 ===");
            
            var processor = new FormulaProcessor();
            
            // 使用反射访问私有方法
            var method = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            if (method == null)
            {
                Console.WriteLine("错误：无法找到 PostProcessLatexFormatting 方法");
                return;
            }
            
            // 测试1：简单分段函数
            Console.WriteLine("测试1：简单分段函数");
            var input1 = @"\begin{cases}x, & \text{if } x > 0 \\ 0, & \text{otherwise}\end{cases}";
            var output1 = (string)method.Invoke(processor, new object[] { input1, true });
            Console.WriteLine($"输入：{input1}");
            Console.WriteLine($"输出：{output1}");
            Console.WriteLine($"保留双反斜杠：{(output1.Contains(@" \\ ") ? "✓" : "✗")}");
            Console.WriteLine();
            
            // 测试2：复杂分段函数（您的示例）
            Console.WriteLine("测试2：复杂分段函数");
            var input2 = @"\begin{cases}0.9 - 0.2\timesmin(T_{consec}(t,d(s)),3), & \text{if } T_{consec}(t,d(s)) > 0 \\ 1, & \text{otherwise}\end{cases}";
            var output2 = (string)method.Invoke(processor, new object[] { input2, true });
            Console.WriteLine($"输入：{input2}");
            Console.WriteLine($"输出：{output2}");
            Console.WriteLine($"保留双反斜杠：{(output2.Contains(@" \\ ") ? "✓" : "✗")}");
            Console.WriteLine($"正确的\\times：{(output2.Contains(@"\times") && !output2.Contains(@"\\times") ? "✓" : "✗")}");
            Console.WriteLine();
            
            // 测试3：三行分段函数
            Console.WriteLine("测试3：三行分段函数");
            var input3 = @"\begin{cases}x^2, & \text{if } x > 0 \\ 0, & \text{if } x = 0 \\ -x^2, & \text{if } x < 0\end{cases}";
            var output3 = (string)method.Invoke(processor, new object[] { input3, true });
            Console.WriteLine($"输入：{input3}");
            Console.WriteLine($"输出：{output3}");
            var lineCount = output3.Split(new string[] { @" \\ " }, StringSplitOptions.None).Length;
            Console.WriteLine($"行数正确（应为3行）：{(lineCount == 3 ? "✓" : "✗")} (实际：{lineCount}行)");
            Console.WriteLine();
            
            Console.WriteLine("=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
