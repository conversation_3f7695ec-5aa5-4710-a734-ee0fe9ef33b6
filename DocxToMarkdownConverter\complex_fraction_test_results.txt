=== 复杂分母分数表达式处理测试 ===

测试1：问题案例 - 复杂分母分数
输入：1000/(1 + 1.5N_{conflict})
输出：\\frac{1000}{1 + 1.5N_{conflict}}
正确分数格式：✓
错误连接文本：✓ 不存在
测试1结果：✓ 通过

测试2：完整分段函数案例
输入：F_{conflict}=\begin{cases}1000, & \text{if }  N_{conflict} = 0 \\ 1000/(1 + 1.5N_{conflict}), & \text{if }  N_{conflict} > 0\end{cases}
输出：F_{conflict}=\begin{cases}1000, & \text{if }  N_{conflict} = 0 \\ \\frac{1000}{1 + 1.5N_{conflict}}, & \text{if }  N_{conflict} > 0\end{cases}
正确分数格式：✓
错误连接文本：✓ 不存在
分段函数格式：✓
测试2结果：✓ 通过

测试3：其他复杂分母格式
输入：a/(b + c*d)
输出：\\frac{a}{b + c*d}
复杂分母转换：✓
测试3结果：✓ 通过

测试4：嵌套括号分母
输入：x/(y + (z - w))
输出：\\frac{x}{y + (z - w})
嵌套括号转换：✗
测试4结果：✗ 失败

=== 测试完成 ===
