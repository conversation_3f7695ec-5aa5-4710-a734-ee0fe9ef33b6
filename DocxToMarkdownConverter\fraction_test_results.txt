=== 分数表达式处理测试 ===

测试1：基本斜杠分数
输入：C_{ideal}/cap(r)
输出：\\frac{C_{ideal}}{cap(r)}
正确的\frac格式：✓

测试2：分段函数中的分数
输入：\begin{cases}5, & \text{if } C_{min} \leq cap(r) \leq C_{ideal} \\ 5\times C_{ideal}/cap(r), & \text{if } cap(r) > C_{ideal}\end{cases}
输出：\begin{cases}5, & \text{if }  C_{\\min} \leq cap(r) \leq C_{ideal} \\ 5\times \\frac{C_{ideal}}{cap(r)}, & \text{if }  cap(r) > C_{ideal}\end{cases}
正确的\frac格式：✓
正确的\times格式：✓
分段函数分数测试：✓ 通过

测试3：复杂分数表达式
输入：a/b + c/d
输出：\\frac{a}{b} + \\frac{c}{d}
第一个分数正确：✓
第二个分数正确：✓
复杂分数测试：✓ 通过

测试4：带括号的分数
输入：(x+y)/(z-w)
输出：\\frac{x + y}{z - w}
带括号分数正确：✓
实际输出包含frac：True
带括号分数测试：✓ 通过

测试5：原始问题案例
输入：f_{cap}(r)=\begin{cases}5, & \text{if } C_{min} \leq cap(r) \leq C_{ideal} \\ 5\times C_{ideal}/cap(r), & \text{if } cap(r) > C_{ideal}\end{cases}
输出：f_{cap}(r)=\begin{cases}5, & \text{if }  C_{\\min} \leq cap(r) \leq C_{ideal} \\ 5\times \\frac{C_{ideal}}{cap(r)}, & \text{if }  cap(r) > C_{ideal}\end{cases}
修复原始问题：✓
不包含错误连接：✓

=== 测试完成 ===
