=== LaTeX反斜杠转义修复测试 ===

测试1：\times符号转义
输入：0.9 - 0.2\timesmin(T_consec(t,d(s)),3)
输出：0.9 - 0.2\times min(T_consec(t,d(s)),3)
输出长度：39
包含 '\times min'：True
包含 '\\times'：False
times前后的字符：'.2\tim'
字符[8]: '2' (ASCII: 50)
字符[9]: '\' (ASCII: 92)
字符[10]: 't' (ASCII: 116)
字符[11]: 'i' (ASCII: 105)
简单测试 - 输入：\timesmin, 输出：\times min
包含单反斜杠版本：True
包含双反斜杠版本：False
正确的\times（单反斜杠）：✓

测试2：比较运算符转义
输入：x≤3 and y≥5
输出：x \leq 3 and y \geq 5
正确的\leq（单反斜杠）：✓
正确的\geq（单反斜杠）：✓

测试3：分段函数中的\times和双反斜杠行分隔符
输入：\begin{cases}0.9 - 0.2\timesmin(T_consec(t,d(s)),3), & \text{if } T_consec(t,d(s)) > 0 \\ 1, & \text{otherwise}\end{cases}
输出：\begin{cases}0.9 - 0.2\times min(T_consec(t,d(s)),3), & \text{if }  T_consec(t,d(s)) > 0 \\ 1, & \text{\text{otherwise}}\end{cases}
正确的\times（单反斜杠）：✓
保留双反斜杠行分隔符：✓
正确的\text{}（单反斜杠）：✓
分段函数综合测试：✓ 通过

测试4：数学函数转义
输入：min(x,y) + max(a,b)
输出：min(x,y) + max(a,b)
正确的\min（单反斜杠）：✗
正确的\max（单反斜杠）：✗

=== 测试完成 ===
