=== 分段函数分数表达式处理测试 ===

测试1：分段函数中的简单分数
输入：\begin{cases}x/y, & \text{if } x > 0 \\ 0, & \text{otherwise}\end{cases}
输出：\begin{cases}\\frac{x}{y}, & \text{if }  x > 0 \\ 0, & \text{\text{otherwise}}\end{cases}
简单分数转换：✓

测试2：分段函数中的下标分数
输入：\begin{cases}C_{ideal}/cap(r), & \text{if } cap(r) > 0 \\ 1, & \text{otherwise}\end{cases}
输出：\begin{cases}\\frac{C_{ideal}}{cap(r)}, & \text{if }  cap(r) > 0 \\ 1, & \text{\text{otherwise}}\end{cases}
下标分数转换：✓

测试3：分段函数中的函数调用分数
输入：\begin{cases}f(x)/g(y), & \text{if } x \neq y \\ h(z), & \text{otherwise}\end{cases}
输出：\begin{cases}\\frac{f(x)}{g(y)}, & \text{if }  x \neq y \\ h(z), & \text{\text{otherwise}}\end{cases}
函数调用分数转换：✓

测试4：分段函数中的复杂表达式分数
输入：\begin{cases}(a+b)/(c-d), & \text{if } c > d \\ 0, & \text{otherwise}\end{cases}
输出：\begin{cases}\\frac{a + b}{c - d}, & \text{if }  c > d \\ 0, & \text{\text{otherwise}}\end{cases}
复杂表达式分数转换：✓

测试5：分段函数中的多个分数
输入：\begin{cases}a/b + c/d, & \text{if } x > 0 \\ e/f, & \text{otherwise}\end{cases}
输出：\begin{cases}\\frac{a}{b} + \\frac{c}{d}, & \text{if }  x > 0 \\ \\frac{e}{f}, & \text{\text{otherwise}}\end{cases}
第一个分数 a/b：✓
第二个分数 c/d：✓
第三个分数 e/f：✓
多个分数测试：✓ 通过

测试6：边缘情况 - 复杂分段函数格式
输入：\begin{cases}5\times C_{ideal}/cap(r), & \text{if } cap(r) > C_{ideal} \\ 1, & \text{otherwise}\end{cases}
输出：\begin{cases}5\times \\frac{C_{ideal}}{cap(r)}, & \text{if }  cap(r) > C_{ideal} \\ 1, & \text{\text{otherwise}}\end{cases}
分数转换：✓
\times 保留：✓
条件格式化：✗
边缘情况测试：✗ 失败

=== 测试完成 ===
