=== 分段函数行分隔符测试 ===

测试1：简单分段函数
输入：\begin{cases}x, & \text{if } x > 0 \\ 0, & \text{otherwise}\end{cases}
输出：\begin{cases}x, & \text{if }  x > 0 \\ 0, & \text{\text{otherwise}}\end{cases}
保留双反斜杠：✓

测试2：复杂分段函数
输入：\begin{cases}0.9 - 0.2\timesmin(T_{consec}(t,d(s)),3), & \text{if } T_{consec}(t,d(s)) > 0 \\ 1, & \text{otherwise}\end{cases}
输出：\begin{cases}0.9 - 0.2\\times min(T_{consec}(t,d(s)),3), & \text{if }  T_{consec}(t,d(s)) > 0 \\ 1, & \text{\text{otherwise}}\end{cases}
保留双反斜杠：✓
正确的\times：✗

测试3：三行分段函数
输入：\begin{cases}x^2, & \text{if } x > 0 \\ 0, & \text{if } x = 0 \\ -x^2, & \text{if } x < 0\end{cases}
输出：\begin{cases}x^2, & \text{if }  x > 0 \\ 0, & \text{if }  x = 0 \\ -x^2, & \text{if }  x < 0\end{cases}
行数正确（应为3行）：✓ (实际：3行)

=== 测试完成 ===
