=== 正则表达式调试测试 ===

测试输入：1000/(1 + 1.5N_{conflict})

Pattern 1: \(([^)]+)\)/\(([^)]+)\)
匹配失败

Pattern 2: \(([^)]+)\)/([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*(?:\([^)]*\))*)
匹配失败

Pattern 3: ([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*|[0-9]+(?:\.[0-9]+)*)/\(([^)]+)\)
匹配成功！
分子：1000
分母：1 + 1.5N_{conflict}
替换结果：\\frac{1000}{1 + 1.5N_{conflict}}

Pattern 4: ([a-zA-Z_][a-zA-Z0-9_]*\([^)]*\))/([a-zA-Z_][a-zA-Z0-9_]*\([^)]*\))
匹配失败

Pattern 5: ([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*(?:\^[^/\s,&]*)*)/([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*(?:\([^)]*\))*(?:\^[^/\s,&]*)*?)(?=[,&\s]|$)
匹配失败

Pattern 6: ([a-zA-Z_][a-zA-Z0-9_]*)/([a-zA-Z_][a-zA-Z0-9_]*)
匹配失败

=== 调试完成 ===
